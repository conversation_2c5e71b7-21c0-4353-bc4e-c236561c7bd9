<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class MattermostService
{
    private $webhookUrl;
    private $enabled;

    public function __construct()
    {
        $this->webhookUrl = env('MATTERMOST_WEBHOOK_URL');
        $this->enabled = env('MATTERMOST_WEBHOOK_ENABLED', false);
    }

    /**
     * Send attendance summary to Mattermost
     */
    public function sendAttendanceSummary($summaryData)
    {
        if (!$this->enabled || empty($this->webhookUrl)) {
            Log::info('MattermostService: Webhook disabled or URL not configured');
            return false;
        }

        try {
            $message = $this->formatAttendanceMessage($summaryData);
            return $this->sendWebhook($message);
        } catch (\Exception $e) {
            Log::error('MattermostService Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Format attendance data into Mattermost message
     */
    private function formatAttendanceMessage($data)
    {
        $date = $data['date'];
        $departmentInfo = $data['department_info'];
        $totalActiveUsers = $data['total_active_users'];
        $physical = $data['physical_attendance'];
        $online = $data['online_attendance'];
        $absence = $data['absence_letters'];
        $overall = $data['overall'];

        // Create message with Mattermost formatting
        $message = [
            'text' => "## 📊 TỔNG HỢP CHUYÊN CẦN NGÀY {$date}{$departmentInfo}",
            'attachments' => [
                [
                    'color' => '#36a64f',
                    'fields' => [
                        [
                            'title' => '👥 Tổng số nhân viên',
                            'value' => $totalActiveUsers . ' người',
                            'short' => true
                        ],
                        [
                            'title' => '📊 Tỷ lệ có thông tin chuyên cần',
                            'value' => $overall['attendance_rate'] . '%',
                            'short' => true
                        ]
                    ]
                ],
                [
                    'color' => '#2196F3',
                    'title' => '🏢 CHẤM CÔNG THỰC TẾ (Camera AI)',
                    'fields' => [
                        [
                            'title' => 'Tổng số người đã chấm công',
                            'value' => $physical['total_users'] . ' người',
                            'short' => true
                        ],
                        [
                            'title' => 'Có đầy đủ check in/out',
                            'value' => $physical['complete_attendance'] . ' người',
                            'short' => true
                        ],
                        [
                            'title' => 'Chấm công đúng giờ',
                            'value' => $physical['on_time'] . ' người',
                            'short' => true
                        ],
                        [
                            'title' => 'Chấm công trễ giờ',
                            'value' => $physical['late'] . ' người',
                            'short' => true
                        ],
                        [
                            'title' => 'Tổng lượt chấm công',
                            'value' => $physical['total_checkins'] . ' lượt',
                            'short' => false
                        ]
                    ]
                ],
                [
                    'color' => '#FF9800',
                    'title' => '💻 CHẤM CÔNG ONLINE',
                    'fields' => [
                        [
                            'title' => 'Tổng đăng ký online',
                            'value' => $online['total_requests'] . ' người',
                            'short' => true
                        ],
                        [
                            'title' => 'Đã được duyệt',
                            'value' => $online['approved'] . ' người',
                            'short' => true
                        ],
                        [
                            'title' => 'Chờ duyệt',
                            'value' => $online['pending'] . ' người',
                            'short' => true
                        ]
                    ]
                ],
                [
                    'color' => '#9C27B0',
                    'title' => '🏖️ NGHỈ PHÉP',
                    'fields' => [
                        [
                            'title' => 'Tổng đơn nghỉ phép',
                            'value' => $absence['total_requests'] . ' đơn',
                            'short' => true
                        ],
                        [
                            'title' => 'Đã được duyệt',
                            'value' => $absence['approved'] . ' đơn',
                            'short' => true
                        ],
                        [
                            'title' => 'Chờ duyệt',
                            'value' => $absence['pending'] . ' đơn',
                            'short' => true
                        ],
                        [
                            'title' => 'Bị từ chối',
                            'value' => $absence['rejected'] . ' đơn',
                            'short' => true
                        ]
                    ]
                ],
                [
                    'color' => '#F44336',
                    'title' => '📈 TỔNG QUAN',
                    'fields' => [
                        [
                            'title' => 'Tổng có mặt',
                            'value' => $overall['total_attended'] . ' người (' . $overall['physical'] . ' chấm công + ' . $overall['online'] . ' online + ' . $overall['absence'] . ' nghỉ phép)',
                            'short' => false
                        ],
                        [
                            'title' => 'Chưa có thông tin',
                            'value' => $overall['not_attended'] . ' người',
                            'short' => true
                        ]
                    ]
                ]
            ]
        ];

        // Add users without attendance info if any
        if ($overall['not_attended'] > 0 && !empty($data['users_without_attendance'])) {
            $usersList = '';
            foreach (array_slice($data['users_without_attendance'], 0, 10) as $user) {
                $usersList .= "• {$user['staff_code']} - {$user['name']} | {$user['department']} | {$user['position']}\n";
            }
            
            if (count($data['users_without_attendance']) > 10) {
                $usersList .= "... và " . (count($data['users_without_attendance']) - 10) . " người khác";
            }

            $message['attachments'][] = [
                'color' => '#FF5722',
                'title' => '❓ DANH SÁCH NHÂN VIÊN CHƯA CÓ THÔNG TIN (Top 10)',
                'text' => $usersList,
                'mrkdwn_in' => ['text']
            ];
        }

        return $message;
    }

    /**
     * Send webhook to Mattermost
     */
    private function sendWebhook($message)
    {
        $ch = curl_init($this->webhookUrl);
        
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($message));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen(json_encode($message))
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            Log::error('MattermostService cURL Error: ' . $error);
            return false;
        }

        if ($httpCode !== 200) {
            Log::error('MattermostService HTTP Error: ' . $httpCode . ' - ' . $response);
            return false;
        }

        Log::info('MattermostService: Attendance summary sent successfully');
        return true;
    }
}
