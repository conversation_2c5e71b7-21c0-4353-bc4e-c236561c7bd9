# Hướng dẫn sử dụng Mattermost Webhook cho Attendance Summary

## Tổng quan

Tính năng này cho phép gửi tự động kết quả tổng hợp chuyên cần hàng ngày về Mattermost thông qua webhook.

## Cài đặt

### 1. Cấu hình Environment Variables

Thêm các biến môi trường sau vào file `.env`:

```bash
# Mattermost Webhook
MATTERMOST_WEBHOOK_URL=https://your-mattermost-server.com/hooks/your-webhook-id
MATTERMOST_WEBHOOK_ENABLED=true
```

### 2. Tạo Webhook trong Mattermost

1. Đăng nhập vào Mattermost
2. Vào **System Console** > **Integrations** > **Incoming Webhooks**
3. Tạo một webhook mới và copy URL
4. Paste URL vào biến `MATTERMOST_WEBHOOK_URL` trong file `.env`

## Sử dụng

### Command Line

```bash
# Tổng hợp chuyên cần và gửi về Mattermost
php artisan attendance:summary --webhook

# Tổng hợp cho phòng ban cụ thể và gửi về Mattermost
php artisan attendance:summary 1 --webhook

# Tổng hợp cho ngày cụ thể và gửi về Mattermost
php artisan attendance:summary 1 2024-01-15 --webhook

# Hiển thị chi tiết và gửi về Mattermost
php artisan attendance:summary --detail --webhook
```

### Các tham số

- `department_id` (tùy chọn): ID phòng ban cần tổng hợp
- `date` (tùy chọn): Ngày cần tổng hợp (format: YYYY-MM-DD), mặc định là hôm nay
- `--detail`: Hiển thị danh sách chi tiết nhân viên
- `--webhook`: Gửi kết quả về Mattermost webhook

## Cấu trúc dữ liệu gửi về Mattermost

Webhook sẽ gửi một message có cấu trúc như sau:

### Thông tin tổng quan
- Tổng số nhân viên đang hoạt động
- Tỷ lệ có thông tin chuyên cần

### Chi tiết theo loại
- **Chấm công thực tế (Camera AI)**: Số người đã chấm công
- **Chấm công online**: Số người đã được duyệt làm việc online
- **Nghỉ phép**: Số đơn nghỉ phép đã được duyệt

### Tổng quan cuối cùng
- Tổng số người có mặt (chấm công + online + nghỉ phép)
- Số người chưa có thông tin
- Tỷ lệ có thông tin chuyên cần

### Danh sách chi tiết người dùng
- **Chi tiết chấm công thực tế**: Top 10 nhân viên đã chấm công với thời gian vào/ra
- **Chi tiết chấm công online**: Top 10 nhân viên làm việc online với thời gian vào/ra
- **Chi tiết nghỉ phép**: Top 10 nhân viên nghỉ phép với loại nghỉ, thời gian và số ngày

### Danh sách nhân viên chưa có thông tin
- Hiển thị top 10 nhân viên chưa có thông tin chuyên cần
- Bao gồm: mã nhân viên, tên, phòng ban, chức vụ

## Tự động hóa

Có thể thiết lập cron job để tự động gửi báo cáo hàng ngày:

```bash
# Thêm vào crontab để chạy lúc 9h sáng hàng ngày
0 9 * * * cd /path/to/your/project && php artisan attendance:summary --webhook
```

## Troubleshooting

### Webhook không hoạt động

1. Kiểm tra `MATTERMOST_WEBHOOK_ENABLED=true`
2. Kiểm tra URL webhook có đúng không
3. Kiểm tra log trong `storage/logs/laravel.log`

### Không có dữ liệu

1. Kiểm tra có nhân viên active không
2. Kiểm tra dữ liệu chấm công trong database
3. Kiểm tra tham số department_id có đúng không

## Files liên quan

- `app/Console/Commands/AttendanceSummaryCommand.php`: Command chính
- `app/Services/MattermostService.php`: Service xử lý webhook
- `.env`: Cấu hình environment variables

## Cấu trúc Message Mattermost

Message được format theo chuẩn Mattermost attachment với:
- Màu sắc khác nhau cho từng loại thông tin
- Hiển thị dạng bảng với các field
- Hỗ trợ markdown formatting
- Giới hạn danh sách nhân viên (top 10 cho mỗi loại)

### Ví dụ cấu trúc dữ liệu gửi về Mattermost:

```
📊 TỔNG HỢP CHUYÊN CẦN NGÀY 2024-01-15 - TẤT CẢ PHÒNG BAN

👥 Tổng số nhân viên: 50 người
📊 Tỷ lệ có thông tin chuyên cần: 85%

🏢 CHẤM CÔNG THỰC TẾ (Camera AI)
Tổng số người đã chấm công: 30 người

💻 CHẤM CÔNG ONLINE
Đã được duyệt: 10 người

🏖️ NGHỈ PHÉP
Đã được duyệt: 3 đơn

📈 TỔNG QUAN
Tổng có mặt: 43 người (30 chấm công + 10 online + 3 nghỉ phép)
Chưa có thông tin: 7 người

📋 CHI TIẾT CHẤM CÔNG THỰC TẾ (Top 10)
• NV001 - Nguyễn Văn A | In: 08:00:00 | Out: 17:30:00
• NV002 - Trần Thị B | In: 08:15:00 | Out: 17:45:00
...

📋 CHI TIẾT CHẤM CÔNG ONLINE (Top 10)
• NV010 - Lê Văn C | In: 08:00:00 | Out: 17:00:00
• NV011 - Phạm Thị D | In: 08:30:00 | Out: 17:30:00
...

📋 CHI TIẾT NGHỈ PHÉP (Top 10)
• NV020 - Hoàng Văn E | Nghỉ phép năm | 15/01/2024 (1 ngày)
• NV021 - Vũ Thị F | Nghỉ ốm | 15/01/2024 - 16/01/2024 (2 ngày)
...

❓ DANH SÁCH NHÂN VIÊN CHƯA CÓ THÔNG TIN (Top 10)
• NV030 - Đỗ Văn G | Phòng IT | Developer
• NV031 - Bùi Thị H | Phòng HR | Nhân viên
...
```
