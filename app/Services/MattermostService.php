<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class MattermostService
{
    private $webhookUrl;
    private $enabled;

    public function __construct()
    {
        $this->webhookUrl = env('MATTERMOST_WEBHOOK_URL');
        $this->enabled = env('MATTERMOST_WEBHOOK_ENABLED', false);
    }

    /**
     * Send attendance summary to Mattermost
     */
    public function sendAttendanceSummary($summaryData)
    {
        if (!$this->enabled || empty($this->webhookUrl)) {
            Log::info('MattermostService: Webhook disabled or URL not configured');
            return false;
        }

        try {
            $message = $this->formatAttendanceMessage($summaryData);
            return $this->sendWebhook($message);
        } catch (\Exception $e) {
            Log::error('MattermostService Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Format attendance data into Mattermost message
     */
    private function formatAttendanceMessage($data)
    {
        $date = $data['date'];
        $departmentInfo = $data['department_info'];
        $totalActiveUsers = $data['total_active_users'];
        $overall = $data['overall'];

        // Create message with Mattermost formatting
        $message = [
            'text' => "# 📊 TỔNG HỢP CHUYÊN CẦN NGÀY {$date}{$departmentInfo}",
            'attachments' => [
                [
                    'color' => '#2E7D32',
                    'title' => '📈 THỐNG KÊ TỔNG QUAN',
                    'fields' => [
                        [
                            'title' => '👥 Tổng số nhân viên đang hoạt động',
                            'value' => "**{$totalActiveUsers}** người",
                            'short' => true
                        ],
                        [
                            'title' => '✅ Tổng có thông tin chuyên cần',
                            'value' => "**{$overall['total_attended']}** người",
                            'short' => true
                        ],
                        [
                            'title' => '❓ Chưa có thông tin',
                            'value' => "**{$overall['not_attended']}** người",
                            'short' => true
                        ],
                        [
                            'title' => '📊 Tỷ lệ có thông tin',
                            'value' => "**{$overall['attendance_rate']}%**",
                            'short' => true
                        ]
                    ]
                ]
            ]
        ];

        // Add combined statistics and details for each category
        $this->addCombinedAttendanceSections($message, $data);

        // 4. DANH SÁCH NHÂN VIÊN CHƯA CÓ THÔNG TIN
        if ($overall['not_attended'] > 0 && !empty($overall['users_without_attendance'])) {
            $notAttendedContent = "**📊 THỐNG KÊ:** {$overall['not_attended']} người chưa có thông tin chuyên cần\n\n";
            $notAttendedContent .= "**📋 DANH SÁCH CHI TIẾT:**\n";
            $notAttendedContent .= "```\n";
            $notAttendedContent .= sprintf("%-8s | %-20s | %-15s | %-15s\n", "Mã NV", "Họ tên", "Phòng ban", "Chức vụ");
            $notAttendedContent .= str_repeat("-", 65) . "\n";

            foreach ($overall['users_without_attendance'] as $user) {
                $notAttendedContent .= sprintf("%-8s | %-20s | %-15s | %-15s\n",
                    $user['staff_code'],
                    mb_substr($user['name'], 0, 20),
                    mb_substr($user['department'], 0, 15),
                    mb_substr($user['position'], 0, 15)
                );
            }
            $notAttendedContent .= "```";

            $message['attachments'][] = [
                'color' => '#D32F2F',
                'title' => '❓ DANH SÁCH NHÂN VIÊN CHƯA CÓ THÔNG TIN',
                'text' => $notAttendedContent,
                'mrkdwn_in' => ['text']
            ];
        }

        return $message;
    }

    /**
     * Add combined statistics and details for each attendance category
     */
    private function addCombinedAttendanceSections(&$message, $data)
    {
        $overall = $data['overall'];

        // 1. THỐNG KÊ CHẤM CÔNG THỰC TẾ + DANH SÁCH
        if (!empty($data['physical_attendance']['details']) || $overall['physical'] > 0) {
            $physicalContent = "**📊 THỐNG KÊ:** {$overall['physical']} người đã chấm công\n\n";

            if (!empty($data['physical_attendance']['details'])) {
                $physicalContent .= "**📋 DANH SÁCH CHI TIẾT:**\n";
                $physicalContent .= "```\n";
                $physicalContent .= sprintf("%-8s | %-50s | %-10s | %-10s\n", "Mã NV", "Họ tên", "Giờ vào", "Giờ ra");
                $physicalContent .= str_repeat("-", 55) . "\n";

                foreach ($data['physical_attendance']['details'] as $user) {
                    $physicalContent .= sprintf("%-8s | %-50s | %-10s | %-10s\n",
                        $user['staff_code'],
                        mb_substr($user['name'], 0, 20),
                        $user['checkin_time'],
                        $user['checkout_time']
                    );
                }
                $physicalContent .= "```";
            } else {
                $physicalContent .= "_Không có dữ liệu chi tiết_";
            }

            $message['attachments'][] = [
                'color' => '#1976D2',
                'title' => '🏢 CHẤM CÔNG THỰC TẾ (DANH SÁCH CHẤM CÔNG THỰC TẾ)',
                'text' => $physicalContent,
                'mrkdwn_in' => ['text']
            ];
        }

        // 2. THỐNG KÊ CHẤM CÔNG ONLINE + DANH SÁCH
        if (!empty($data['online_attendance']['details']) || $overall['online'] > 0) {
            $onlineContent = "**📊 THỐNG KÊ:** {$overall['online']} người đã được duyệt làm việc online\n\n";

            if (!empty($data['online_attendance']['details'])) {
                $onlineContent .= "**📋 DANH SÁCH CHI TIẾT:**\n";
                $onlineContent .= "```\n";
                $onlineContent .= sprintf("%-8s | %-50s | %-10s | %-10s\n", "Mã NV", "Họ tên", "Giờ vào", "Giờ ra");
                $onlineContent .= str_repeat("-", 55) . "\n";

                foreach ($data['online_attendance']['details'] as $user) {
                    $onlineContent .= sprintf("%-8s | %-50s | %-10s | %-10s\n",
                        $user['staff_code'],
                        mb_substr($user['name'], 0, 20),
                        $user['checkin_time'],
                        $user['checkout_time']
                    );
                }
                $onlineContent .= "```";
            } else {
                $onlineContent .= "_Không có dữ liệu chi tiết_";
            }

            $message['attachments'][] = [
                'color' => '#F57C00',
                'title' => '💻 CHẤM CÔNG ONLINE (DANH SÁCH CHẤM CÔNG ONLINE)',
                'text' => $onlineContent,
                'mrkdwn_in' => ['text']
            ];
        }

        // 3. THỐNG KÊ NGHỈ PHÉP + DANH SÁCH
        if (!empty($data['absence_letters']['details']) || $overall['absence'] > 0) {
            $absenceContent = "**📊 THỐNG KÊ:** {$overall['absence']} đơn nghỉ phép đã được duyệt\n\n";

            if (!empty($data['absence_letters']['details'])) {
                $absenceContent .= "**📋 DANH SÁCH CHI TIẾT:**\n";
                $absenceContent .= "```\n";
                $absenceContent .= sprintf("%-8s | %-50s | %-15s | %-12s | %-4s\n", "Mã NV", "Họ tên", "Loại nghỉ", "Thời gian", "Ngày");
                $absenceContent .= str_repeat("-", 70) . "\n";

                foreach ($data['absence_letters']['details'] as $user) {
                    $dateRange = $user['from_date'] === $user['to_date'] ? $user['from_date'] : "{$user['from_date']}-{$user['to_date']}";
                    $absenceContent .= sprintf("%-8s | %-50s | %-15s | %-12s | %-4s\n",
                        $user['staff_code'],
                        mb_substr($user['name'], 0, 20),
                        mb_substr($user['leave_type'], 0, 15),
                        $dateRange,
                        $user['days']
                    );
                }
                $absenceContent .= "```";
            } else {
                $absenceContent .= "_Không có dữ liệu chi tiết_";
            }

            $message['attachments'][] = [
                'color' => '#7B1FA2',
                'title' => '🏖️ THỐNG KÊ NGHỈ PHÉP (DANH SÁCH NGHỈ PHÉP)',
                'text' => $absenceContent,
                'mrkdwn_in' => ['text']
            ];
        }
    }

    /**
     * Send webhook to Mattermost
     */
    private function sendWebhook($message)
    {
        $ch = curl_init($this->webhookUrl);
        
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($message));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen(json_encode($message))
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            Log::error('MattermostService cURL Error: ' . $error);
            return false;
        }

        if ($httpCode !== 200) {
            Log::error('MattermostService HTTP Error: ' . $httpCode . ' - ' . $response);
            return false;
        }

        Log::info('MattermostService: Attendance summary sent successfully');
        return true;
    }
}
