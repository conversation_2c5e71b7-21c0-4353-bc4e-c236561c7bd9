<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\InOutsOnline;
use App\Models\AbsenceLetter;
use App\Models\PersonInOut;
use App\Services\MattermostService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AttendanceSummaryCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'attendance:summary {department_id?} {date?} {--detail : Hi<PERSON><PERSON> thị danh sách chi tiết nhân viên} {--webhook : <PERSON><PERSON><PERSON> kết quả về Mattermost webhook}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Tổng hợp chuyên cần của User hàng ngày theo phòng ban (check in/out, online, nghỉ phép)';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            // Xử lý tham số department_id, date và option detail, webhook
            $departmentId = $this->argument('department_id');
            $dateInput = $this->argument('date');
            $showDetail = $this->option('detail');
            $sendWebhook = $this->option('webhook');

            if (!$dateInput || $dateInput === 'now') {
                $date = Carbon::now()->format('Y-m-d');
            } else {
                // Validate date format
                try {
                    $date = Carbon::createFromFormat('Y-m-d', $dateInput)->format('Y-m-d');
                } catch (\Exception $e) {
                    $this->error('Định dạng ngày không hợp lệ. Vui lòng sử dụng định dạng YYYY-MM-DD');
                    return 1;
                }
            }

            $departmentInfo = $departmentId ? " - PHÒNG BAN ID: {$departmentId}" : " - TẤT CẢ PHÒNG BAN";
            $this->info("=== TỔNG HỢP CHUYÊN CẦN NGÀY {$date}{$departmentInfo} ===");
            $this->line('');

            // Lấy danh sách tất cả nhân viên đang active (không bao gồm admin)
            $totalActiveUsers = $this->getTotalActiveUsers($departmentId);
            $this->info("📊 Tổng số nhân viên đang hoạt động: {$totalActiveUsers}");
            $this->line('');

            // 1. Tổng hợp check in/out thực tế (từ camera AI)
            $this->info('🏢 CHẤM CÔNG THỰC TẾ (Camera AI):');
            $this->summarizePhysicalAttendance($date, $departmentId, $showDetail);
            $this->line('');

            // 2. Tổng hợp check in/out online
            $this->info('💻 CHẤM CÔNG ONLINE:');
            $this->summarizeOnlineAttendance($date, $departmentId, $showDetail);
            $this->line('');

            // 3. Tổng hợp nghỉ phép
            $this->info('🏖️ NGHỈ PHÉP:');
            $this->summarizeAbsenceLetters($date, $departmentId, $showDetail);
            $this->line('');

            // 4. Tổng hợp tổng quan
            $this->info('📈 TỔNG QUAN:');
            $overallData = $this->summarizeOverall($date, $totalActiveUsers, $departmentId, true);

            $this->line('');
            $this->info('✅ Hoàn thành tổng hợp chuyên cần!');

            dump($overallData);
            // Gửi kết quả về Mattermost webhook nếu được yêu cầu
            if ($sendWebhook && $overallData) {
                $this->line('');
                $this->info('📤 Đang gửi kết quả về Mattermost...');
                $this->sendToMattermost($overallData);
            }
        } catch (\Exception $e) {
            $this->error('Lỗi khi thực hiện tổng hợp chuyên cần: ' . $e->getMessage());
            Log::error('AttendanceSummaryCommand Error: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * Lấy tổng số nhân viên đang hoạt động
     */
    private function getTotalActiveUsers($departmentId = null)
    {
        return User::notAdmin()
            ->whereHas('workings', function ($query) use ($departmentId) {
                if ($departmentId) {
                    $query->where('department_id', $departmentId);
                }
            })
            ->count();
    }

    /**
     * Tổng hợp chấm công thực tế từ camera AI
     */
    private function summarizePhysicalAttendance($date, $departmentId = null, $showDetail = false)
    {
        // Lấy danh sách staff_code của nhân viên theo department_id nếu có
        $staffCodes = null;
        if ($departmentId) {
            $staffCodes = User::notAdmin()
                ->whereHas('workings', function ($query) use ($departmentId) {
                    $query->where('department_id', $departmentId);
                })
                ->pluck('staff_code')
                ->filter()
                ->toArray();
        }

        // Lấy danh sách nhân viên đã chấm công trong ngày từ PersonInOut
        $attendanceData = PersonInOut::select(DB::raw('
            aliasID,
            COUNT(DISTINCT aliasID) as total_users,
            MIN(time) as first_checkin,
            MAX(time) as last_checkout
            '))
            ->where('date', 'LIKE', $date . '%')
            ->where('create_by', PersonInOut::CREARE_BY_CAMAI)
            ->when($staffCodes, function ($query, $staffCodes) {
                $query->whereIn('aliasID', $staffCodes);
            })
            ->groupBy('aliasID')
            ->get();

        $totalUsers = $attendanceData->count();

        // Đếm số người có check in và check out đầy đủ
        $completeAttendance = 0;
        $onTimeAttendance = 0;
        $lateAttendance = 0;

        // Thời gian chuẩn (8:00 AM và 3:00 PM)
        $standardCheckinTime = Carbon::parse($date)->setTime(8, 0, 0);
        $standardCheckoutTime = Carbon::parse($date)->setTime(15, 0, 0);

        foreach ($attendanceData as $record) {
            // Kiểm tra có đầy đủ check in và check out không
            if ($record->first_checkin && $record->last_checkout && $record->first_checkin != $record->last_checkout) {
                $completeAttendance++;

                // Convert timestamp to Carbon (PersonInOut stores time as milliseconds)
                $firstCheckin = is_numeric($record->first_checkin) ? Carbon::createFromTimestamp($record->first_checkin / 1000) : null;
                $lastCheckout = is_numeric($record->last_checkout) ? Carbon::createFromTimestamp($record->last_checkout / 1000) : null;

                // Kiểm tra chấm công đúng giờ
                if ($firstCheckin && $lastCheckout &&
                    $firstCheckin->lte($standardCheckinTime)) {
                    $onTimeAttendance++;
                } else {
                    $lateAttendance++;
                }
            }
        }

        // Tổng số lượt check in/out trong ngày
        $totalCheckInOuts = PersonInOut::where('date', 'LIKE', $date . '%')
            ->where('create_by', PersonInOut::CREARE_BY_CAMAI)
            ->when($staffCodes, function ($query, $staffCodes) {
                $query->whereIn('aliasID', $staffCodes);
            })
            ->count();

        $this->line("   👥 Tổng số người đã chấm công: {$totalUsers} người");
        $this->line("   ✅ Có đầy đủ check in/out: {$completeAttendance} người");
        $this->line("   🟢 Chấm công đúng giờ: {$onTimeAttendance} người");
        $this->line("   🟡 Chấm công trễ giờ: {$lateAttendance} người");
        $this->line("   📊 Tổng lượt chấm công: {$totalCheckInOuts} lượt");

        // Hiển thị danh sách chi tiết nếu được yêu cầu
        if ($showDetail && $totalUsers > 0) {
            $this->line('');
            $this->line('   📋 DANH SÁCH CHI TIẾT:');

            foreach ($attendanceData as $record) {
                // Lấy thông tin user từ staff_code
                $user = User::where('staff_code', $record->aliasID)->first();
                $userName = $user ? $user->name : 'N/A';

                $checkinTime = ($record->first_checkin && is_numeric($record->first_checkin)) ? date('H:i:s', $record->first_checkin / 1000) : 'N/A';
                $checkoutTime = ($record->last_checkout && is_numeric($record->last_checkout)) ? date('H:i:s', $record->last_checkout / 1000) : 'N/A';

                // Xác định trạng thái
                $status = '❓';
                if ($record->first_checkin && $record->last_checkout && $record->first_checkin != $record->last_checkout) {
                    // Convert timestamp to Carbon for comparison
                    $firstCheckinCarbon = is_numeric($record->first_checkin) ? Carbon::createFromTimestamp($record->first_checkin / 1000) : null;
                    $lastCheckoutCarbon = is_numeric($record->last_checkout) ? Carbon::createFromTimestamp($record->last_checkout / 1000) : null;

                    if ($firstCheckinCarbon && $lastCheckoutCarbon &&
                        $firstCheckinCarbon->lte($standardCheckinTime) && $lastCheckoutCarbon->gte($standardCheckoutTime)) {
                        $status = '🟢';
                    } else {
                        $status = '🟡';
                    }
                }

                $this->line("      {$status} {$record->aliasID} - {$userName} | In: {$checkinTime} | Out: {$checkoutTime}");
            }
        }
    }

    /**
     * Tổng hợp chấm công online
     */
    private function summarizeOnlineAttendance($date, $departmentId = null, $showDetail = false)
    {
        // Lấy danh sách user_id theo department_id nếu có
        $userIds = null;
        if ($departmentId) {
            $userIds = User::notAdmin()
                ->whereHas('workings', function ($query) use ($departmentId) {
                    $query->where('department_id', $departmentId);
                })
                ->pluck('id')
                ->toArray();
        }

        // Tổng số đăng ký làm việc online trong ngày
        $totalOnlineRequests = InOutsOnline::whereDate('checkin', $date)
            ->when($userIds, function ($query, $userIds) {
                $query->whereIn('user_id', $userIds);
            })
            ->count();

        // Số đăng ký đã được duyệt (status = 2 hoặc có approve_time)
        $approvedOnline = InOutsOnline::whereDate('checkin', $date)
            ->where(function ($query) {
                $query->where('status', 2)
                    ->orWhereNotNull('approve_time');
            })
            ->when($userIds, function ($query, $userIds) {
                $query->whereIn('user_id', $userIds);
            })
            ->count();

        // Số đăng ký chờ duyệt (status = 1 và chưa có approve_time)
        $pendingOnline = InOutsOnline::whereDate('checkin', $date)
            ->where('status', 1)
            ->whereNull('approve_time')
            ->when($userIds, function ($query, $userIds) {
                $query->whereIn('user_id', $userIds);
            })
            ->count();

        $this->line("   📝 Tổng đăng ký online: {$totalOnlineRequests} người");
        $this->line("   ✅ Đã được duyệt: {$approvedOnline} người");
        $this->line("   ⏳ Chờ duyệt: {$pendingOnline} người");

        // Hiển thị danh sách chi tiết nếu được yêu cầu
        if ($showDetail && $totalOnlineRequests > 0) {
            $this->line('');
            $this->line('   📋 DANH SÁCH CHI TIẾT:');

            $onlineRecords = InOutsOnline::with('user:id,name,staff_code')
                ->whereDate('checkin', $date)
                ->when($userIds, function ($query, $userIds) {
                    $query->whereIn('user_id', $userIds);
                })
                ->orderBy('checkin')
                ->get();

            foreach ($onlineRecords as $record) {
                $userName = $record->user ? $record->user->name : 'N/A';
                $staffCode = $record->user ? $record->user->staff_code : 'N/A';
                $checkinTime = $record->checkin ? Carbon::parse($record->checkin)->format('H:i:s') : 'N/A';
                $checkoutTime = $record->checkout ? Carbon::parse($record->checkout)->format('H:i:s') : 'N/A';

                // Xác định trạng thái
                $status = '⏳';
                if ($record->status == 2 || $record->approve_time) {
                    $status = '✅';
                } elseif ($record->status == 1) {
                    $status = '⏳';
                }

                $this->line("      {$status} {$staffCode} - {$userName} | In: {$checkinTime} | Out: {$checkoutTime}");
            }
        }
    }

    /**
     * Tổng hợp nghỉ phép
     */
    private function summarizeAbsenceLetters($date, $departmentId = null, $showDetail = false)
    {
        // Lấy danh sách user_id theo department_id nếu có
        $userIds = null;
        if ($departmentId) {
            $userIds = User::notAdmin()
                ->whereHas('workings', function ($query) use ($departmentId) {
                    $query->where('department_id', $departmentId);
                })
                ->pluck('id')
                ->toArray();
        }

        // Tổng số đơn nghỉ phép trong ngày
        $totalAbsenceRequests = AbsenceLetter::where('from_date', '<=', $date)
            ->where('to_date', '>=', $date)
            ->when($userIds, function ($query, $userIds) {
                $query->whereIn('user_id', $userIds);
            })
            ->count();

        // Số đơn đã được duyệt
        $approvedAbsence = AbsenceLetter::where('from_date', '<=', $date)
            ->where('to_date', '>=', $date)
            ->where('status', AbsenceLetter::STATUS_APPROVE)
            ->when($userIds, function ($query, $userIds) {
                $query->whereIn('user_id', $userIds);
            })
            ->count();

        // Số đơn chờ duyệt
        $pendingAbsence = AbsenceLetter::where('from_date', '<=', $date)
            ->where('to_date', '>=', $date)
            ->where('status', AbsenceLetter::STATUS_WAIT_APPROVE)
            ->when($userIds, function ($query, $userIds) {
                $query->whereIn('user_id', $userIds);
            })
            ->count();

        // Số đơn bị từ chối
        $rejectedAbsence = AbsenceLetter::where('from_date', '<=', $date)
            ->where('to_date', '>=', $date)
            ->where('status', AbsenceLetter::STATUS_REJECT)
            ->when($userIds, function ($query, $userIds) {
                $query->whereIn('user_id', $userIds);
            })
            ->count();

        $this->line("   📋 Tổng đơn nghỉ phép: {$totalAbsenceRequests} đơn");
        $this->line("   ✅ Đã được duyệt: {$approvedAbsence} đơn");
        $this->line("   ⏳ Chờ duyệt: {$pendingAbsence} đơn");
        $this->line("   ❌ Bị từ chối: {$rejectedAbsence} đơn");

        // Hiển thị danh sách chi tiết nếu được yêu cầu
        if ($showDetail && $totalAbsenceRequests > 0) {
            $this->line('');
            $this->line('   📋 DANH SÁCH CHI TIẾT:');

            $absenceRecords = AbsenceLetter::with(['user:id,name,staff_code', 'absence_letter_type:id,name'])
                ->where('from_date', '<=', $date)
                ->where('to_date', '>=', $date)
                ->when($userIds, function ($query, $userIds) {
                    $query->whereIn('user_id', $userIds);
                })
                ->orderBy('from_date')
                ->get();

            foreach ($absenceRecords as $record) {
                $userName = $record->user ? $record->user->name : 'N/A';
                $staffCode = $record->user ? $record->user->staff_code : 'N/A';
                $leaveType = $record->absence_letter_type ? $record->absence_letter_type->name : 'N/A';
                $fromDate = $record->from_date ? Carbon::parse($record->from_date)->format('d/m/Y') : 'N/A';
                $toDate = $record->to_date ? Carbon::parse($record->to_date)->format('d/m/Y') : 'N/A';
                $days = $record->number_days ?? 0;

                // Xác định trạng thái
                $status = '⏳';
                if ($record->status == AbsenceLetter::STATUS_APPROVE) {
                    $status = '✅';
                } elseif ($record->status == AbsenceLetter::STATUS_REJECT) {
                    $status = '❌';
                } elseif ($record->status == AbsenceLetter::STATUS_WAIT_APPROVE) {
                    $status = '⏳';
                }

                $dateRange = $fromDate === $toDate ? $fromDate : "{$fromDate} - {$toDate}";
                $this->line("      {$status} {$staffCode} - {$userName} | {$leaveType} | {$dateRange} ({$days} ngày)");
            }
        }
    }

    /**
     * Tổng hợp tổng quan
     */
    private function summarizeOverall($date, $totalActiveUsers, $departmentId = null, $collectData = false)
    {
        // Lấy danh sách staff_code và user_id theo department_id nếu có
        $staffCodes = null;
        $userIds = null;
        if ($departmentId) {
            $users = User::notAdmin()
                ->whereHas('workings', function ($query) use ($departmentId) {
                    $query->where('department_id', $departmentId);
                })
                ->get(['id', 'staff_code']);

            $staffCodes = $users->pluck('staff_code')->filter()->toArray();
            $userIds = $users->pluck('id')->toArray();
        }
        //dump($staffCodes);
        // Số người đã có hoạt động chấm công thực tế (từ PersonInOut)
        $physicalAttendance = PersonInOut::select('aliasID')
            ->where('date', 'LIKE', $date . '%')
            //->where('create_by', PersonInOut::CREARE_BY_CAMAI)
            ->when($staffCodes, function ($query, $staffCodes) {
                $query->whereIn('aliasID', $staffCodes);
            })
            ->groupBy('aliasID')
            ->get()->count();
        //dd($physicalAttendance->count());
        //dd($physicalAttendance->toArray());
        // Số người làm việc online đã được duyệt
        $onlineAttendance = InOutsOnline::whereDate('checkin', $date)
            ->where(function ($query) {
                $query->where('status', 2)
                    ->orWhereNotNull('approve_time');
            })
            ->when($userIds, function ($query, $userIds) {
                $query->whereIn('user_id', $userIds);
            })
            ->get()->count();

        // Số người nghỉ phép đã được duyệt
        $approvedAbsence = AbsenceLetter::where('from_date', '<=', $date)
            ->where('to_date', '>=', $date)
            ->where('status', AbsenceLetter::STATUS_APPROVE)
            ->when($userIds, function ($query, $userIds) {
                $query->whereIn('user_id', $userIds);
            })
            ->get()->count();

        $totalAttended = (int)$physicalAttendance + (int)$onlineAttendance + (int)$approvedAbsence;
        $notAttended = (int)$totalActiveUsers - $totalAttended;

        $attendanceRate = ($totalActiveUsers > 0 && is_numeric($totalActiveUsers) && is_numeric($totalAttended))
            ? round(($totalAttended / $totalActiveUsers) * 100, 2) : 0;

        $this->line("   👥 Tổng có mặt ({$physicalAttendance} chấm công  + {$onlineAttendance} online + {$approvedAbsence} nghỉ phép): {$totalAttended} người");
        $this->line("   ❓ Chưa có thông tin: {$notAttended} người");
        $this->line("   📊 Tỷ lệ có thông tin chuyên cần: {$attendanceRate}%");

        // Hiển thị danh sách chi tiết những người chưa có thông tin
        $usersWithoutAttendance = [];
        if ($notAttended > 0) {
            $this->line('');
            $this->line('   📋 DANH SÁCH NHÂN VIÊN CHƯA CÓ THÔNG tin:');
            // Luôn thu thập danh sách chi tiết khi gửi webhook hoặc khi có option --detail
            $usersWithoutAttendance = $this->showUsersWithoutAttendance($date, $departmentId, $staffCodes, $userIds, $collectData || true);
        }

        // Trả về dữ liệu nếu được yêu cầu để gửi webhook
        if ($collectData) {
            $departmentInfo = $departmentId ? " - PHÒNG BAN ID: {$departmentId}" : " - TẤT CẢ PHÒNG BAN";

            // Thu thập danh sách chi tiết cho từng loại
            $physicalAttendanceDetails = $this->getPhysicalAttendanceDetails($date, $staffCodes);
            $onlineAttendanceDetails = $this->getOnlineAttendanceDetails($date, $userIds);
            $absenceLetterDetails = $this->getAbsenceLetterDetails($date, $userIds);

            return [
                'date' => $date,
                'department_info' => $departmentInfo,
                'total_active_users' => $totalActiveUsers,
                'physical_attendance' => [
                    'total_users' => $physicalAttendance,
                    'details' => $physicalAttendanceDetails
                ],
                'online_attendance' => [
                    'total_approved' => $onlineAttendance,
                    'details' => $onlineAttendanceDetails
                ],
                'absence_letters' => [
                    'total_approved' => $approvedAbsence,
                    'details' => $absenceLetterDetails
                ],
                'overall' => [
                    'physical' => $physicalAttendance,
                    'online' => $onlineAttendance,
                    'absence' => $approvedAbsence,
                    'total_attended' => $totalAttended,
                    'not_attended' => $notAttended,
                    'attendance_rate' => $attendanceRate,
                    'users_without_attendance' => $usersWithoutAttendance
                ],
                'users_without_attendance' => $usersWithoutAttendance
            ];
        }

        return null;
    }

    /**
     * Hiển thị danh sách nhân viên chưa có thông tin chuyên cần
     */
    private function showUsersWithoutAttendance($date, $departmentId = null, $staffCodes = null, $userIds = null, $returnData = false)
    {
        // Lấy danh sách tất cả nhân viên active
        $allUsersQuery = User::with(['workings.department', 'workings.position'])
            ->notAdmin()
            ->whereHas('workings', function ($query) use ($departmentId) {
                if ($departmentId) {
                    $query->where('department_id', $departmentId);
                }
            });

        $allUsers = $allUsersQuery->get();

        // Lấy danh sách staff_code đã có chấm công thực tế
        $physicalAttendanceStaffCodes = PersonInOut::select('aliasID')
            ->where('date', 'LIKE', $date . '%')
            ->where('create_by', PersonInOut::CREARE_BY_CAMAI)
            ->when($staffCodes, function ($query, $staffCodes) {
                $query->whereIn('aliasID', $staffCodes);
            })
            ->groupBy('aliasID')
            ->pluck('aliasID')
            ->toArray();

        // Lấy danh sách user_id đã có chấm công online được duyệt
        $onlineAttendanceUserIds = InOutsOnline::whereDate('checkin', $date)
            ->where(function ($query) {
                $query->where('status', 2)
                    ->orWhereNotNull('approve_time');
            })
            ->when($userIds, function ($query, $userIds) {
                $query->whereIn('user_id', $userIds);
            })
            ->pluck('user_id')
            ->toArray();

        // Lấy danh sách user_id đã có nghỉ phép được duyệt
        $absenceUserIds = AbsenceLetter::where('from_date', '<=', $date)
            ->where('to_date', '>=', $date)
            ->where('status', AbsenceLetter::STATUS_APPROVE)
            ->when($userIds, function ($query, $userIds) {
                $query->whereIn('user_id', $userIds);
            })
            ->pluck('user_id')
            ->toArray();

        // Lọc ra những nhân viên chưa có thông tin
        $usersWithoutAttendance = $allUsers->filter(function ($user) use ($physicalAttendanceStaffCodes, $onlineAttendanceUserIds, $absenceUserIds) {
            $hasPhysicalAttendance = in_array($user->staff_code, $physicalAttendanceStaffCodes);
            $hasOnlineAttendance = in_array($user->id, $onlineAttendanceUserIds);
            $hasAbsence = in_array($user->id, $absenceUserIds);

            return !$hasPhysicalAttendance && !$hasOnlineAttendance && !$hasAbsence;
        });

        // Hiển thị danh sách và chuẩn bị dữ liệu trả về
        $usersData = [];
        foreach ($usersWithoutAttendance as $user) {
            $departmentName = 'N/A';
            $positionName = 'N/A';

            if ($user->workings->isNotEmpty()) {
                $working = $user->workings->first();
                $departmentName = $working->department ? $working->department->name : 'N/A';
                $positionName = $working->position ? $working->position->name : 'N/A';
            }

            $this->line("      ❓ {$user->staff_code} - {$user->name} | {$departmentName} | {$positionName}");

            // Chuẩn bị dữ liệu để trả về cho webhook
            if ($returnData) {
                $usersData[] = [
                    'staff_code' => $user->staff_code,
                    'name' => $user->name,
                    'department' => $departmentName,
                    'position' => $positionName
                ];
            }
        }

        return $returnData ? $usersData : null;
    }

    /**
     * Lấy danh sách chi tiết chấm công thực tế
     */
    private function getPhysicalAttendanceDetails($date, $staffCodes = null)
    {
        $attendanceData = PersonInOut::select(DB::raw('
            aliasID,
            MIN(time) as first_checkin,
            MAX(time) as last_checkout
            '))
            ->where('date', 'LIKE', $date . '%')
            ->where('create_by', PersonInOut::CREARE_BY_CAMAI)
            ->when($staffCodes, function ($query, $staffCodes) {
                $query->whereIn('aliasID', $staffCodes);
            })
            ->groupBy('aliasID')
            ->get();

        $details = [];
        foreach ($attendanceData as $record) {
            $user = User::where('staff_code', $record->aliasID)->first();
            if ($user) {
                $checkinTime = ($record->first_checkin && is_numeric($record->first_checkin)) ? date('H:i:s', $record->first_checkin / 1000) : 'N/A';
                $checkoutTime = ($record->last_checkout && is_numeric($record->last_checkout)) ? date('H:i:s', $record->last_checkout / 1000) : 'N/A';

                $details[] = [
                    'staff_code' => $record->aliasID,
                    'name' => $user->name,
                    'checkin_time' => $checkinTime,
                    'checkout_time' => $checkoutTime
                ];
            }
        }

        return $details;
    }

    /**
     * Lấy danh sách chi tiết chấm công online
     */
    private function getOnlineAttendanceDetails($date, $userIds = null)
    {
        $onlineRecords = InOutsOnline::with('user:id,name,staff_code')
            ->whereDate('checkin', $date)
            ->where(function ($query) {
                $query->where('status', 2)
                    ->orWhereNotNull('approve_time');
            })
            ->when($userIds, function ($query, $userIds) {
                $query->whereIn('user_id', $userIds);
            })
            ->get();

        $details = [];
        foreach ($onlineRecords as $record) {
            if ($record->user) {
                $checkinTime = $record->checkin ? Carbon::parse($record->checkin)->format('H:i:s') : 'N/A';
                $checkoutTime = $record->checkout ? Carbon::parse($record->checkout)->format('H:i:s') : 'N/A';

                $details[] = [
                    'staff_code' => $record->user->staff_code,
                    'name' => $record->user->name,
                    'checkin_time' => $checkinTime,
                    'checkout_time' => $checkoutTime
                ];
            }
        }

        return $details;
    }

    /**
     * Lấy danh sách chi tiết nghỉ phép
     */
    private function getAbsenceLetterDetails($date, $userIds = null)
    {
        $absenceRecords = AbsenceLetter::with(['user:id,name,staff_code', 'absence_letter_type:id,name'])
            ->where('from_date', '<=', $date)
            ->where('to_date', '>=', $date)
            ->where('status', AbsenceLetter::STATUS_APPROVE)
            ->when($userIds, function ($query, $userIds) {
                $query->whereIn('user_id', $userIds);
            })
            ->get();

        $details = [];
        foreach ($absenceRecords as $record) {
            if ($record->user) {
                $leaveType = $record->absence_letter_type ? $record->absence_letter_type->name : 'N/A';
                $fromDate = $record->from_date ? Carbon::parse($record->from_date)->format('d/m/Y') : 'N/A';
                $toDate = $record->to_date ? Carbon::parse($record->to_date)->format('d/m/Y') : 'N/A';
                $days = $record->number_days ?? 0;

                $details[] = [
                    'staff_code' => $record->user->staff_code,
                    'name' => $record->user->name,
                    'leave_type' => $leaveType,
                    'from_date' => $fromDate,
                    'to_date' => $toDate,
                    'days' => $days
                ];
            }
        }

        return $details;
    }

    /**
     * Gửi kết quả tổng hợp về Mattermost webhook
     */
    private function sendToMattermost($overallData)
    {
        try {
            $mattermostService = new MattermostService();

            $result = $mattermostService->sendAttendanceSummary($overallData);

            if ($result) {
                $this->info('✅ Đã gửi kết quả về Mattermost thành công!');
            } else {
                $this->error('❌ Không thể gửi kết quả về Mattermost. Vui lòng kiểm tra log để biết chi tiết.');
            }
        } catch (\Exception $e) {
            $this->error('❌ Lỗi khi gửi về Mattermost: ' . $e->getMessage());
            Log::error('AttendanceSummaryCommand sendToMattermost Error: ' . $e->getMessage());
        }
    }
}
