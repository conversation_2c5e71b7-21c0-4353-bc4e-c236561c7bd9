<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class MattermostService
{
    private $webhookUrl;
    private $enabled;

    public function __construct()
    {
        $this->webhookUrl = env('MATTERMOST_WEBHOOK_URL');
        $this->enabled = env('MATTERMOST_WEBHOOK_ENABLED', false);
    }

    /**
     * Send attendance summary to Mattermost
     */
    public function sendAttendanceSummary($summaryData)
    {
        if (!$this->enabled || empty($this->webhookUrl)) {
            Log::info('MattermostService: Webhook disabled or URL not configured');
            return false;
        }

        try {
            $message = $this->formatAttendanceMessage($summaryData);
            return $this->sendWebhook($message);
        } catch (\Exception $e) {
            Log::error('MattermostService Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Format attendance data into Mattermost message
     */
    private function formatAttendanceMessage($data)
    {
        $date = $data['date'];
        $departmentInfo = $data['department_info'];
        $totalActiveUsers = $data['total_active_users'];
        $overall = $data['overall'];

        // Create message with Mattermost formatting
        $message = [
            'text' => "## 📊 TỔNG HỢP CHUYÊN CẦN NGÀY {$date}{$departmentInfo}",
            'attachments' => [
                [
                    'color' => '#36a64f',
                    'fields' => [
                        [
                            'title' => '👥 Tổng số nhân viên',
                            'value' => $totalActiveUsers . ' người',
                            'short' => true
                        ],
                        [
                            'title' => '📊 Tỷ lệ có thông tin chuyên cần',
                            'value' => $overall['attendance_rate'] . '%',
                            'short' => true
                        ]
                    ]
                ],
                [
                    'color' => '#2196F3',
                    'title' => '🏢 CHẤM CÔNG THỰC TẾ (Camera AI)',
                    'fields' => [
                        [
                            'title' => 'Tổng số người đã chấm công',
                            'value' => $overall['physical'] . ' người',
                            'short' => true
                        ]
                    ]
                ],
                [
                    'color' => '#FF9800',
                    'title' => '💻 CHẤM CÔNG ONLINE',
                    'fields' => [
                        [
                            'title' => 'Đã được duyệt',
                            'value' => $overall['online'] . ' người',
                            'short' => true
                        ]
                    ]
                ],
                [
                    'color' => '#9C27B0',
                    'title' => '🏖️ NGHỈ PHÉP',
                    'fields' => [
                        [
                            'title' => 'Đã được duyệt',
                            'value' => $overall['absence'] . ' đơn',
                            'short' => true
                        ]
                    ]
                ],
                [
                    'color' => '#F44336',
                    'title' => '📈 TỔNG QUAN',
                    'fields' => [
                        [
                            'title' => 'Tổng có mặt',
                            'value' => $overall['total_attended'] . ' người (' . $overall['physical'] . ' chấm công + ' . $overall['online'] . ' online + ' . $overall['absence'] . ' nghỉ phép)',
                            'short' => false
                        ],
                        [
                            'title' => 'Chưa có thông tin',
                            'value' => $overall['not_attended'] . ' người',
                            'short' => true
                        ]
                    ]
                ]
            ]
        ];

        // Add detailed lists for each category
        $this->addDetailedAttendanceLists($message, $data);

        // Add users without attendance info if any
        if ($overall['not_attended'] > 0 && !empty($overall['users_without_attendance'])) {
            $usersList = '';
            foreach (array_slice($overall['users_without_attendance'], 0, 10) as $user) {
                $usersList .= "• {$user['staff_code']} - {$user['name']} | {$user['department']} | {$user['position']}\n";
            }

            if (count($overall['users_without_attendance']) > 10) {
                $usersList .= "... và " . (count($overall['users_without_attendance']) - 10) . " người khác";
            }

            $message['attachments'][] = [
                'color' => '#FF5722',
                'title' => '❓ DANH SÁCH NHÂN VIÊN CHƯA CÓ THÔNG TIN (Top 10)',
                'text' => $usersList,
                'mrkdwn_in' => ['text']
            ];
        }

        return $message;
    }

    /**
     * Add detailed attendance lists to message
     */
    private function addDetailedAttendanceLists(&$message, $data)
    {
        // Physical attendance details
        if (!empty($data['physical_attendance']['details'])) {
            $physicalList = '';
            foreach (array_slice($data['physical_attendance']['details'], 0, 10) as $user) {
                $physicalList .= "• {$user['staff_code']} - {$user['name']} | In: {$user['checkin_time']} | Out: {$user['checkout_time']}\n";
            }

            if (count($data['physical_attendance']['details']) > 10) {
                $physicalList .= "... và " . (count($data['physical_attendance']['details']) - 10) . " người khác";
            }

            $message['attachments'][] = [
                'color' => '#2196F3',
                'title' => '📋 CHI TIẾT CHẤM CÔNG THỰC TẾ (Top 10)',
                'text' => $physicalList,
                'mrkdwn_in' => ['text']
            ];
        }

        // Online attendance details
        if (!empty($data['online_attendance']['details'])) {
            $onlineList = '';
            foreach (array_slice($data['online_attendance']['details'], 0, 10) as $user) {
                $onlineList .= "• {$user['staff_code']} - {$user['name']} | In: {$user['checkin_time']} | Out: {$user['checkout_time']}\n";
            }

            if (count($data['online_attendance']['details']) > 10) {
                $onlineList .= "... và " . (count($data['online_attendance']['details']) - 10) . " người khác";
            }

            $message['attachments'][] = [
                'color' => '#FF9800',
                'title' => '📋 CHI TIẾT CHẤM CÔNG ONLINE (Top 10)',
                'text' => $onlineList,
                'mrkdwn_in' => ['text']
            ];
        }

        // Absence letter details
        if (!empty($data['absence_letters']['details'])) {
            $absenceList = '';
            foreach (array_slice($data['absence_letters']['details'], 0, 10) as $user) {
                $dateRange = $user['from_date'] === $user['to_date'] ? $user['from_date'] : "{$user['from_date']} - {$user['to_date']}";
                $absenceList .= "• {$user['staff_code']} - {$user['name']} | {$user['leave_type']} | {$dateRange} ({$user['days']} ngày)\n";
            }

            if (count($data['absence_letters']['details']) > 10) {
                $absenceList .= "... và " . (count($data['absence_letters']['details']) - 10) . " người khác";
            }

            $message['attachments'][] = [
                'color' => '#9C27B0',
                'title' => '📋 CHI TIẾT NGHỈ PHÉP (Top 10)',
                'text' => $absenceList,
                'mrkdwn_in' => ['text']
            ];
        }
    }

    /**
     * Send webhook to Mattermost
     */
    private function sendWebhook($message)
    {
        $ch = curl_init($this->webhookUrl);
        
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($message));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen(json_encode($message))
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            Log::error('MattermostService cURL Error: ' . $error);
            return false;
        }

        if ($httpCode !== 200) {
            Log::error('MattermostService HTTP Error: ' . $httpCode . ' - ' . $response);
            return false;
        }

        Log::info('MattermostService: Attendance summary sent successfully');
        return true;
    }
}
