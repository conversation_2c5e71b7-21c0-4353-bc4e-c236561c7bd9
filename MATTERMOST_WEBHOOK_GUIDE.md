# Hướng dẫn sử dụng Mattermost Webhook cho Attendance Summary

## Tổng quan

Tính năng này cho phép gửi tự động kết quả tổng hợp chuyên cần hàng ngày về Mattermost thông qua webhook.

## Cài đặt

### 1. Cấu hình Environment Variables

Thêm các biến môi trường sau vào file `.env`:

```bash
# Mattermost Webhook
MATTERMOST_WEBHOOK_URL=https://your-mattermost-server.com/hooks/your-webhook-id
MATTERMOST_WEBHOOK_ENABLED=true
```

### 2. Tạo Webhook trong Mattermost

1. Đăng nhập vào Mattermost
2. Vào **System Console** > **Integrations** > **Incoming Webhooks**
3. Tạo một webhook mới và copy URL
4. Paste URL vào biến `MATTERMOST_WEBHOOK_URL` trong file `.env`

## Sử dụng

### Command Line

```bash
# Tổng hợp chuyên cần và gửi về Mattermost
php artisan attendance:summary --webhook

# Tổng hợp cho phòng ban cụ thể và gửi về Mattermost
php artisan attendance:summary 1 --webhook

# Tổng hợp cho ngày cụ thể và gửi về Mattermost
php artisan attendance:summary 1 2024-01-15 --webhook

# Hiển thị chi tiết và gửi về Mattermost
php artisan attendance:summary --detail --webhook
```

### Các tham số

- `department_id` (tùy chọn): ID phòng ban cần tổng hợp
- `date` (tùy chọn): Ngày cần tổng hợp (format: YYYY-MM-DD), mặc định là hôm nay
- `--detail`: Hiển thị danh sách chi tiết nhân viên
- `--webhook`: Gửi kết quả về Mattermost webhook

## Cấu trúc dữ liệu gửi về Mattermost

Webhook sẽ gửi một message có cấu trúc như sau:

### PHẦN 1: THỐNG KÊ TỔNG QUÁT

#### 1.1 Thống kê tổng quan
- Tổng số nhân viên đang hoạt động
- Tổng có thông tin chuyên cần
- Số người chưa có thông tin
- Tỷ lệ có thông tin chuyên cần

#### 1.2 Thống kê theo từng loại
- **Thống kê chấm công thực tế (Camera AI)**: Số người đã chấm công
- **Thống kê chấm công online**: Số người đã được duyệt làm việc online
- **Thống kê nghỉ phép**: Số đơn nghỉ phép đã được duyệt

### PHẦN 2: CHI TIẾT DANH SÁCH NHÂN VIÊN

#### 2.1 Danh sách chi tiết người dùng (HIỂN THỊ ĐẦY ĐỦ)
- **Danh sách chấm công thực tế**: Tất cả nhân viên đã chấm công với thời gian vào/ra (dạng bảng)
- **Danh sách chấm công online**: Tất cả nhân viên làm việc online với thời gian vào/ra (dạng bảng)
- **Danh sách nghỉ phép**: Tất cả nhân viên nghỉ phép với loại nghỉ, thời gian và số ngày (dạng bảng)

#### 2.2 Danh sách nhân viên chưa có thông tin (HIỂN THỊ ĐẦY ĐỦ)
- Hiển thị tất cả nhân viên chưa có thông tin chuyên cần (dạng bảng)
- Bao gồm: mã nhân viên, tên, phòng ban, chức vụ

## Tự động hóa

Có thể thiết lập cron job để tự động gửi báo cáo hàng ngày:

```bash
# Thêm vào crontab để chạy lúc 9h sáng hàng ngày
0 9 * * * cd /path/to/your/project && php artisan attendance:summary --webhook
```

## Troubleshooting

### Webhook không hoạt động

1. Kiểm tra `MATTERMOST_WEBHOOK_ENABLED=true`
2. Kiểm tra URL webhook có đúng không
3. Kiểm tra log trong `storage/logs/laravel.log`

### Không có dữ liệu

1. Kiểm tra có nhân viên active không
2. Kiểm tra dữ liệu chấm công trong database
3. Kiểm tra tham số department_id có đúng không

## Files liên quan

- `app/Console/Commands/AttendanceSummaryCommand.php`: Command chính
- `app/Services/MattermostService.php`: Service xử lý webhook
- `.env`: Cấu hình environment variables

## Cấu trúc Message Mattermost

Message được format theo chuẩn Mattermost attachment với:
- **Màu sắc đẹp mắt**: Mỗi loại thông tin có màu riêng biệt
- **Dạng bảng chuyên nghiệp**: Sử dụng code block với format bảng cố định
- **Hiển thị đầy đủ**: Không giới hạn số lượng, hiển thị tất cả nhân viên
- **Thông tin chi tiết**: Bao gồm mã nhân viên, tên, thời gian, phòng ban, chức vụ
- **Hỗ trợ Unicode**: Hiển thị đúng tiếng Việt và emoji

### Ví dụ cấu trúc dữ liệu gửi về Mattermost:

```
# 📊 TỔNG HỢP CHUYÊN CẦN NGÀY 2024-01-15 - TẤT CẢ PHÒNG BAN

📈 THỐNG KÊ TỔNG QUAN
👥 Tổng số nhân viên đang hoạt động: **50** người
✅ Tổng có thông tin chuyên cần: **43** người
❓ Chưa có thông tin: **7** người
📊 Tỷ lệ có thông tin: **86%**

🏢 THỐNG KÊ CHẤM CÔNG THỰC TẾ (Camera AI)
👤 Số người đã chấm công: **30** người

💻 THỐNG KÊ CHẤM CÔNG ONLINE
✅ Đã được duyệt: **10** người

🏖️ THỐNG KÊ NGHỈ PHÉP
📋 Đã được duyệt: **3** đơn

📋 CHI TIẾT DANH SÁCH NHÂN VIÊN
_Dưới đây là danh sách chi tiết từng loại chuyên cần_

🏢 DANH SÁCH CHẤM CÔNG THỰC TẾ (30 người)
```
Mã NV    | Họ tên               | Giờ vào    | Giờ ra
-------------------------------------------------------
NV001    | Nguyễn Văn A         | 08:00:00   | 17:30:00
NV002    | Trần Thị B           | 08:15:00   | 17:45:00
NV003    | Lê Văn C             | 07:45:00   | 17:15:00
... (hiển thị tất cả 30 người)
```

💻 DANH SÁCH CHẤM CÔNG ONLINE (10 người)
```
Mã NV    | Họ tên               | Giờ vào    | Giờ ra
-------------------------------------------------------
NV010    | Phạm Thị D           | 08:00:00   | 17:00:00
NV011    | Hoàng Văn E          | 08:30:00   | 17:30:00
... (hiển thị tất cả 10 người)
```

🏖️ DANH SÁCH NGHỈ PHÉP (3 đơn)
```
Mã NV    | Họ tên               | Loại nghỉ      | Thời gian    | Ngày
----------------------------------------------------------------------
NV020    | Vũ Thị F             | Nghỉ phép năm   | 15/01/2024   | 1
NV021    | Đỗ Văn G             | Nghỉ ốm        | 15/01-16/01  | 2
... (hiển thị tất cả 3 đơn)
```

❓ DANH SÁCH NHÂN VIÊN CHƯA CÓ THÔNG TIN (7 người)
```
Mã NV    | Họ tên               | Phòng ban       | Chức vụ
-----------------------------------------------------------------
NV030    | Bùi Thị H            | Phòng IT        | Developer
NV031    | Trần Văn I           | Phòng HR        | Nhân viên
... (hiển thị tất cả 7 người)
```
```
