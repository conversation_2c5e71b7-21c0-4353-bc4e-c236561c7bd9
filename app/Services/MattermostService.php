<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class MattermostService
{
    private $webhookUrl;
    private $enabled;

    public function __construct()
    {
        $this->webhookUrl = env('MATTERMOST_WEBHOOK_URL');
        $this->enabled = env('MATTERMOST_WEBHOOK_ENABLED', false);
    }

    /**
     * Send attendance summary to Mattermost
     */
    public function sendAttendanceSummary($summaryData)
    {
        if (!$this->enabled || empty($this->webhookUrl)) {
            Log::info('MattermostService: Webhook disabled or URL not configured');
            return false;
        }

        try {
            $message = $this->formatAttendanceMessage($summaryData);
            return $this->sendWebhook($message);
        } catch (\Exception $e) {
            Log::error('MattermostService Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Format attendance data into Mattermost message
     */
    private function formatAttendanceMessage($data)
    {
        $date = $data['date'];
        $departmentInfo = $data['department_info'];
        $totalActiveUsers = $data['total_active_users'];
        $overall = $data['overall'];

        // Create message with Mattermost formatting
        $message = [
            'text' => "# 📊 TỔNG HỢP CHUYÊN CẦN NGÀY {$date}{$departmentInfo}",
            'attachments' => [
                [
                    'color' => '#2E7D32',
                    'title' => '📈 THỐNG KÊ TỔNG QUAN',
                    'fields' => [
                        [
                            'title' => '👥 Tổng số nhân viên đang hoạt động',
                            'value' => "**{$totalActiveUsers}** người",
                            'short' => true
                        ],
                        [
                            'title' => '✅ Tổng có thông tin chuyên cần',
                            'value' => "**{$overall['total_attended']}** người",
                            'short' => true
                        ],
                        [
                            'title' => '❓ Chưa có thông tin',
                            'value' => "**{$overall['not_attended']}** người",
                            'short' => true
                        ],
                        [
                            'title' => '📊 Tỷ lệ có thông tin',
                            'value' => "**{$overall['attendance_rate']}%**",
                            'short' => true
                        ]
                    ]
                ],
                [
                    'color' => '#1976D2',
                    'title' => '🏢 CHẤM CÔNG THỰC TẾ (Camera AI)',
                    'fields' => [
                        [
                            'title' => '👤 Số người đã chấm công',
                            'value' => "**{$overall['physical']}** người",
                            'short' => true
                        ]
                    ]
                ],
                [
                    'color' => '#F57C00',
                    'title' => '💻 CHẤM CÔNG ONLINE',
                    'fields' => [
                        [
                            'title' => '✅ Đã được duyệt',
                            'value' => "**{$overall['online']}** người",
                            'short' => true
                        ]
                    ]
                ],
                [
                    'color' => '#7B1FA2',
                    'title' => '🏖️ NGHỈ PHÉP',
                    'fields' => [
                        [
                            'title' => '📋 Đã được duyệt',
                            'value' => "**{$overall['absence']}** đơn",
                            'short' => true
                        ]
                    ]
                ]
            ]
        ];

        // Add detailed lists for each category
        $this->addDetailedAttendanceLists($message, $data);

        // Add users without attendance info if any
        if ($overall['not_attended'] > 0 && !empty($overall['users_without_attendance'])) {
            $usersList = "```\n";
            $usersList .= sprintf("%-8s | %-20s | %-15s | %-15s\n", "Mã NV", "Họ tên", "Phòng ban", "Chức vụ");
            $usersList .= str_repeat("-", 65) . "\n";

            foreach ($overall['users_without_attendance'] as $user) {
                $usersList .= sprintf("%-8s | %-20s | %-15s | %-15s\n",
                    $user['staff_code'],
                    mb_substr($user['name'], 0, 20),
                    mb_substr($user['department'], 0, 15),
                    mb_substr($user['position'], 0, 15)
                );
            }
            $usersList .= "```";

            $notAttendedCount = count($overall['users_without_attendance']);
            $message['attachments'][] = [
                'color' => '#D32F2F',
                'title' => "❓ DANH SÁCH NHÂN VIÊN CHƯA CÓ THÔNG TIN ({$notAttendedCount} người)",
                'text' => $usersList,
                'mrkdwn_in' => ['text']
            ];
        }

        return $message;
    }

    /**
     * Add detailed attendance lists to message
     */
    private function addDetailedAttendanceLists(&$message, $data)
    {
        // Physical attendance details
        if (!empty($data['physical_attendance']['details'])) {
            $physicalList = "```\n";
            $physicalList .= sprintf("%-8s | %-20s | %-10s | %-10s\n", "Mã NV", "Họ tên", "Giờ vào", "Giờ ra");
            $physicalList .= str_repeat("-", 55) . "\n";

            foreach ($data['physical_attendance']['details'] as $user) {
                $physicalList .= sprintf("%-8s | %-20s | %-10s | %-10s\n",
                    $user['staff_code'],
                    mb_substr($user['name'], 0, 20),
                    $user['checkin_time'],
                    $user['checkout_time']
                );
            }
            $physicalList .= "```";

            $physicalCount = count($data['physical_attendance']['details']);
            $message['attachments'][] = [
                'color' => '#1976D2',
                'title' => "🏢 CHI TIẾT CHẤM CÔNG THỰC TẾ ({$physicalCount} người)",
                'text' => $physicalList,
                'mrkdwn_in' => ['text']
            ];
        }

        // Online attendance details
        if (!empty($data['online_attendance']['details'])) {
            $onlineList = "```\n";
            $onlineList .= sprintf("%-8s | %-20s | %-10s | %-10s\n", "Mã NV", "Họ tên", "Giờ vào", "Giờ ra");
            $onlineList .= str_repeat("-", 55) . "\n";

            foreach ($data['online_attendance']['details'] as $user) {
                $onlineList .= sprintf("%-8s | %-20s | %-10s | %-10s\n",
                    $user['staff_code'],
                    mb_substr($user['name'], 0, 20),
                    $user['checkin_time'],
                    $user['checkout_time']
                );
            }
            $onlineList .= "```";

            $onlineCount = count($data['online_attendance']['details']);
            $message['attachments'][] = [
                'color' => '#F57C00',
                'title' => "💻 CHI TIẾT CHẤM CÔNG ONLINE ({$onlineCount} người)",
                'text' => $onlineList,
                'mrkdwn_in' => ['text']
            ];
        }

        // Absence letter details
        if (!empty($data['absence_letters']['details'])) {
            $absenceList = "```\n";
            $absenceList .= sprintf("%-8s | %-20s | %-15s | %-12s | %-4s\n", "Mã NV", "Họ tên", "Loại nghỉ", "Thời gian", "Ngày");
            $absenceList .= str_repeat("-", 70) . "\n";

            foreach ($data['absence_letters']['details'] as $user) {
                $dateRange = $user['from_date'] === $user['to_date'] ? $user['from_date'] : "{$user['from_date']}-{$user['to_date']}";
                $absenceList .= sprintf("%-8s | %-20s | %-15s | %-12s | %-4s\n",
                    $user['staff_code'],
                    mb_substr($user['name'], 0, 20),
                    mb_substr($user['leave_type'], 0, 15),
                    $dateRange,
                    $user['days']
                );
            }
            $absenceList .= "```";

            $absenceCount = count($data['absence_letters']['details']);
            $message['attachments'][] = [
                'color' => '#7B1FA2',
                'title' => "🏖️ CHI TIẾT NGHỈ PHÉP ({$absenceCount} đơn)",
                'text' => $absenceList,
                'mrkdwn_in' => ['text']
            ];
        }
    }

    /**
     * Send webhook to Mattermost
     */
    private function sendWebhook($message)
    {
        $ch = curl_init($this->webhookUrl);
        
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($message));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen(json_encode($message))
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            Log::error('MattermostService cURL Error: ' . $error);
            return false;
        }

        if ($httpCode !== 200) {
            Log::error('MattermostService HTTP Error: ' . $httpCode . ' - ' . $response);
            return false;
        }

        Log::info('MattermostService: Attendance summary sent successfully');
        return true;
    }
}
